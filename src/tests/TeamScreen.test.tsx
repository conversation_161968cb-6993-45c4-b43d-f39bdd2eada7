import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { ThemeProvider } from 'styled-components/native';
import TeamScreen from '../screens/TeamScreen';
import { ManagerProvider } from '../context/ManagerContext';
import { TeamInputMethodProvider } from '../context/TeamInputMethodContext';
import { theme } from '../theme/theme';

// Mock the hooks and dependencies
jest.mock('../hooks/useQueries', () => ({
  useTeam: jest.fn(() => ({ data: null })),
}));

jest.mock('../hooks/useUpdatePlayerOrder', () => ({
  useUpdatePlayerOrder: jest.fn(() => ({
    updatePlayerOrder: jest.fn(),
    isSaving: false,
  })),
}));

jest.mock('../components/DraggablePlayerList', () => ({
  DraggablePlayerList: ({ onSelect, initialScrollPosition }: any) => {
    const mockPlayer = {
      playerId: 'test-player-1',
      firstName: 'Test',
      surname: 'Player',
      value: 100000,
    };

    return (
      <div
        testID="draggable-player-list"
        data-initial-scroll-position={initialScrollPosition}
        onClick={() => onSelect && onSelect(mockPlayer, 150)} // Simulate scroll position of 150
      >
        Mock Player List
      </div>
    );
  },
}));

jest.mock('../components/PlayerDetailView', () => {
  return function MockPlayerDetailView({ onClose }: any) {
    return (
      <div testID="player-detail-view" onClick={onClose}>
        Mock Player Detail View
      </div>
    );
  };
});

const mockManager = {
  managerId: 'test-manager',
  gameworldId: 'test-gameworld',
  team: {
    teamId: 'test-team',
    players: [
      {
        playerId: 'test-player-1',
        firstName: 'Test',
        surname: 'Player',
        value: 100000,
        position: 'Midfielder',
        attributes: {
          stamina: 80,
        },
        energy: 100,
        lastMatchPlayed: null,
      },
    ],
  },
};

const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <ThemeProvider theme={theme}>
    <ManagerProvider>
      <TeamInputMethodProvider>
        {children}
      </TeamInputMethodProvider>
    </ManagerProvider>
  </ThemeProvider>
);

describe('TeamScreen Scroll Position', () => {
  beforeEach(() => {
    // Mock the ManagerContext to return our test data
    jest.spyOn(require('../context/ManagerContext'), 'useManager').mockReturnValue({
      team: mockManager.team,
      loading: false,
      manager: mockManager,
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should preserve scroll position when navigating back from player detail', () => {
    const { getByTestId } = render(
      <TestWrapper>
        <TeamScreen />
      </TestWrapper>
    );

    // Initially, the DraggablePlayerList should have scroll position 0
    const playerList = getByTestId('draggable-player-list');
    expect(playerList.props['data-initial-scroll-position']).toBe(0);

    // Simulate selecting a player (which should save scroll position of 150)
    fireEvent.press(playerList);

    // Player detail view should be shown
    const playerDetailView = getByTestId('player-detail-view');
    expect(playerDetailView).toBeTruthy();

    // Navigate back by closing the player detail view
    fireEvent.press(playerDetailView);

    // The DraggablePlayerList should now have the saved scroll position
    const updatedPlayerList = getByTestId('draggable-player-list');
    expect(updatedPlayerList.props['data-initial-scroll-position']).toBe(150);
  });

  it('should reset scroll position to 0 when component first mounts', () => {
    const { getByTestId } = render(
      <TestWrapper>
        <TeamScreen />
      </TestWrapper>
    );

    const playerList = getByTestId('draggable-player-list');
    expect(playerList.props['data-initial-scroll-position']).toBe(0);
  });
});
