import { ScoutedPlayer } from '../hooks/useQueries';
import { TransferListPlayer } from '../hooks/useTransferListPlayers';
import { calculateBestPosition as calculatePlayerBestPosition, Player } from '../models/player';

export const STAMINA_SCALAR = 5.8;

// Type that can handle both Player and ScoutedPlayer
export type PlayerLike = Player | ScoutedPlayer | TransferListPlayer;

// Check if an object is a Player (from team) or ScoutedPlayer
export function isTeamPlayer(player: PlayerLike): player is Player {
  return 'gameworldId' in player && !('lastScoutedAt' in player);
}

// Check if an object is a TransferListPlayer
export function isTransferListPlayer(player: PlayerLike): player is TransferListPlayer {
  return 'auctionCurrentPrice' in player && 'auctionEndTime' in player && 'bidHistory' in player;
}

// Calculate best position for any player type
export function calculatePlayerPosition(player: PlayerLike): string[] {
  return calculatePlayerBestPosition(player as Player); // Now we can use the same function for both types
}

// Format player value to display format
export function formatPlayerValue(value: number): string {
  if (value < 1000000) {
    return `${(value / 1000).toFixed(0)}K`;
  }
  return `${(value / 1000000).toFixed(1)}M`;
}

export function calculateCurrentEnergy(stamina: number, energy: number, lastMatchPlayed: number) {
  const hoursSinceLastMatch = (Date.now() - lastMatchPlayed) / (1000 * 60 * 60);
  const staminaScalar = stamina * STAMINA_SCALAR;
  return Math.floor(Math.min(100, energy + staminaScalar * hoursSinceLastMatch));
}

export function calculateEnergyByNextMatch(
  stamina: number,
  energy: number,
  lastMatchPlayed: number,
  nextMatchTime: number
) {
  if (nextMatchTime === 0) {
    return 100;
  }
  const hoursBetweenMatches = (nextMatchTime - lastMatchPlayed) / (1000 * 60 * 60);
  const staminaScalar = stamina * STAMINA_SCALAR;
  return Math.floor(Math.min(100, energy + staminaScalar * hoursBetweenMatches));
}
