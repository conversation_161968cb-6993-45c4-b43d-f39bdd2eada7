import { MaterialCommunityIcons, MaterialIcons } from '@expo/vector-icons';
import React from 'react';
import { Dimensions, Image, View } from 'react-native';
import styled, { useTheme } from 'styled-components/native';
import { useManager } from '../../context/ManagerContext';
import {
  PlayerLike,
  calculateCurrentEnergy,
  calculateEnergyByNextMatch,
  calculatePlayerPosition,
  formatPlayerValue,
} from '../../utils/PlayerUtils';
import {
  AttributeGroup,
  AttributeLabel,
  AttributeValue,
  AttributesContainer,
  CardHeader,
  EnergyContainer,
  EnergyText,
  PlayerName,
  PlayerPosition,
  PlayerValue,
  StatusIconContainer,
} from './PlayerRowStyles';

interface PlayerInfoProps {
  player: PlayerLike;
  positionFilter?: 'All' | 'Goalkeeper' | 'Defender' | 'Midfielder' | 'Attacker';
  showImages?: boolean; // Whether to show injury/suspension images or icons
}

const PlayerInfo: React.FC<PlayerInfoProps> = ({
  player,
  positionFilter = 'All',
  showImages = false,
}) => {
  const { team } = useManager();
  const positions = calculatePlayerPosition(player);
  const formattedValue = formatPlayerValue(player.value);

  // Determine which attributes to show based on position filter
  const getAttributesToShow = () => {
    const { attributes } = player;

    switch (positionFilter) {
      case 'Goalkeeper':
        return [
          { label: 'Ref', value: attributes.reflexes },
          { label: 'Pos', value: attributes.positioning },
          { label: 'Stp', value: attributes.shotStopping },
        ];
      case 'Defender':
        return [
          { label: 'Tck', value: attributes.tackling },
          { label: 'Mrk', value: attributes.marking },
          { label: 'Hdr', value: attributes.heading },
        ];
      case 'Midfielder':
        return [
          { label: 'Pas', value: attributes.passing },
          { label: 'Vis', value: attributes.vision },
          { label: 'Ctl', value: attributes.ballControl },
        ];
      case 'Attacker':
        return [
          { label: 'Fin', value: attributes.finishing },
          { label: 'Pac', value: attributes.pace },
          { label: 'Crs', value: attributes.crossing },
        ];
      default:
        return [];
    }
  };

  const attributesToShow = getAttributesToShow();
  const showAttributes = attributesToShow.length > 0;

  // Check screen width to determine if we're on mobile
  const { width } = Dimensions.get('window');
  const isMobile = width < 768;

  // Check if player is injured or suspended
  const isInjured = player.injuredUntil && player.injuredUntil > Date.now();
  const isSuspended = player.suspendedForGames > 0;
  const isRetiring = player.retiringAtEndOfSeason;

  // Calculate current fitness/energy
  const currentEnergy = calculateCurrentEnergy(
    player.attributes.stamina,
    player.energy,
    player.lastMatchPlayed
  );

  const forecastEnergy = team
    ? calculateEnergyByNextMatch(
        player.attributes.stamina,
        player.energy,
        player.lastMatchPlayed,
        team.nextFixture.date
      )
    : 0;

  // Styled component for energy value with dynamic color logic and React Native text shadow for readability
  const EnergyValue = styled.Text<{ energy: number }>`
    color: ${({ energy }) => {
      const clamped = Math.max(0, Math.min(100, energy));
      const r = Math.round(255 * (1 - clamped / 100));
      const g = Math.round(200 * (clamped / 100));
      return `rgb(${r},${g},0)`;
    }};
    font-weight: bold;
    text-shadow-color: ${(props) => props.theme.colors.shadow};
    text-shadow-offset: 2px 2px;
    text-shadow-radius: 2px;
  `;

  return (
    <View style={{ flex: 1 }}>
      <CardHeader hasAttributes={showAttributes} isMobile={isMobile}>
        {!showAttributes && <PlayerPosition>{positions[0] || 'N/A'}</PlayerPosition>}
        {(isInjured || isSuspended || isRetiring) && (
          <StatusIconContainer>
            {isInjured &&
              (showImages ? (
                <Image
                  source={require('../../../assets/injury.png')}
                  style={{ width: 16, height: 16 }}
                  resizeMode="contain"
                />
              ) : (
                <MaterialIcons name="healing" size={16} color="#ffffff" />
              ))}
            {isSuspended && (
              <View
                style={{ position: 'relative', alignItems: 'center', justifyContent: 'center' }}
              >
                {showImages ? (
                  <Image
                    source={require('../../../assets/redcard.png')}
                    style={{ width: 16, height: 16 }}
                    resizeMode="contain"
                  />
                ) : (
                  <MaterialCommunityIcons name="card" size={16} color="#f44336" />
                )}
              </View>
            )}
            {isRetiring && showImages && (
              <View
                style={{ position: 'relative', alignItems: 'center', justifyContent: 'center' }}
              >
                <Image
                  source={require('../../../assets/retiring.png')}
                  style={{ width: 16, height: 16 }}
                  resizeMode="contain"
                />
              </View>
            )}
          </StatusIconContainer>
        )}
        <PlayerName
          isMobile={isMobile}
          isUnavailable={isInjured || isSuspended}
          style={{ flex: showAttributes ? 1 : undefined, textAlign: 'left' }}
        >
          {`${player.firstName} ${player.surname}`}
        </PlayerName>
      </CardHeader>
      {!showAttributes && (
        <AttributesContainer isMobile={isMobile} style={{ alignSelf: 'flex-start' }}>
          <EnergyContainer>
            <EnergyText>
              Energy:{' '}
              {currentEnergy < 100 ? (
                <>
                  <EnergyValue energy={currentEnergy}>{`${currentEnergy}%`}</EnergyValue>
                  {' → '}
                  <EnergyValue energy={forecastEnergy}>{`${forecastEnergy}%`}</EnergyValue>
                </>
              ) : (
                <EnergyValue energy={currentEnergy}>{`${currentEnergy}%`}</EnergyValue>
              )}
            </EnergyText>
            <PlayerValue>{formattedValue}</PlayerValue>
          </EnergyContainer>
        </AttributesContainer>
      )}
      {showAttributes && (
        <AttributesContainer isMobile={isMobile} style={{ alignSelf: 'flex-start' }}>
          {attributesToShow.map((attr, index) => (
            <AttributeGroup key={index}>
              <AttributeLabel>{attr.label}:</AttributeLabel>
              <AttributeValue>{attr.value}</AttributeValue>
            </AttributeGroup>
          ))}
        </AttributesContainer>
      )}
    </View>
  );
};

export default PlayerInfo;
